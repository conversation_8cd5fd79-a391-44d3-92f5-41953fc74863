package com.kikitrade.activity.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.activity.api.exception.ActivityExceptionType;
import com.kikitrade.activity.api.model.ActivityResponse;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.dal.mysql.model.Activity;
import com.kikitrade.activity.dal.mysql.model.SchedLog;
import com.kikitrade.activity.dal.redis.RedisKeyConst;
import com.kikitrade.activity.dal.redis.RedisService;
import com.kikitrade.activity.dal.tablestore.builder.ActivityCustomRewardStoreBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityRecordsBuilder;
import com.kikitrade.activity.dal.tablestore.builder.ActivityUserTotalDataBuilder;
import com.kikitrade.activity.dal.tablestore.model.ActivityCustomReward;
import com.kikitrade.activity.dal.tablestore.model.ActivityUserTotalData;
import com.kikitrade.activity.dal.tablestore.param.ActivityRewardPageParam;
import com.kikitrade.activity.luck.service.business.ReleaseService;
import com.kikitrade.activity.model.ActivityEventMassage;
import com.kikitrade.activity.model.ActivityMessage;
import com.kikitrade.activity.model.FiatDepositActivityConfig;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.constant.ActivityTaskConstant;
import com.kikitrade.activity.model.response.ActivityResponseCode;
import com.kikitrade.activity.model.util.TimeUtil;
import com.kikitrade.activity.service.auth.AuthService;
import com.kikitrade.activity.service.business.ActivityService;
import com.kikitrade.activity.service.business.KolExtraRewardService;
import com.kikitrade.activity.service.business.SchedLogService;
import com.kikitrade.activity.service.common.config.ThreePlatformProperties;
import com.kikitrade.activity.service.common.model.JsonResult;
import com.kikitrade.activity.service.importing.roster.domain.LauncherParameter;
import com.kikitrade.activity.service.job.BreadMemberRankingJob;
import com.kikitrade.activity.service.job.MemberRankingJob;
import com.kikitrade.activity.service.job.ZeekMemberRankingJob;
import com.kikitrade.activity.service.model.OpenSocialPlatformEnum;
import com.kikitrade.activity.service.mq.TopicConfig;
import com.kikitrade.activity.service.reward.RewardService;
import com.kikitrade.activity.service.reward.model.RewardRequest;
import com.kikitrade.activity.service.task.ActivityRealTimeRewardTccService;
import com.kikitrade.framework.common.model.Page;
import com.kikitrade.framework.ons.OnsProducer;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.kcustomer.api.model.TCustomerDTO;
import com.kikitrade.kcustomer.api.service.RemoteCustomerBindService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.remoting.TimeoutException;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Controller
@Slf4j
@RequestMapping("/activity")
public class SchedulerController {

    @Resource
    private ActivityService activityService;

    @Resource
    private ActivityUserTotalDataBuilder activityUserTotalDataBuilder;

    @Resource
    private OnsProducer onsProducer;

    @Resource
    private TopicConfig topicConfig;

    @Resource
    private ActivityRecordsBuilder activityRecordsBuilder;

    @Resource
    private SchedLogService schedLogService;

    @Resource
    private KolExtraRewardService kolExtraRewardService;

    @Resource
    private ReleaseService releaseService;

    @Resource
    private AuthService authService;

    @Resource
    private ActivityRealTimeRewardTccService activityRealTimeRewardTccService;

    @Resource
    private ActivityCustomRewardStoreBuilder activityCustomRewardStoreBuilder;

    @DubboReference
    private RemoteCustomerBindService remoteCustomerBindService;

    @Resource
    private RedisService redisService;

    @Resource
    private ThreePlatformProperties threePlatformProperties;


    /**
     * curl  http://127.0.0.1:8050/activity/create  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":22,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币累计入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币累计入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":5000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":50},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":10000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":100}],\\\"frequency\\\":1}\",\"rule_id\":22}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":0,\"top_order\":1,\"type\":22}"
     *
     * @param activity
     * @return
     */
    @RequestMapping("/create")
    @ResponseBody
    public Boolean create(@RequestBody Activity activity) {
        log.info("create...{}", JSON.toJSONString(activity));
        try {
            activity.setSaas_id(activity.getSaas_id());
            activityService.save(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/edit  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":22,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币累计入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币累计入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":5000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":50},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":10000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":100}],\\\"frequency\\\":1}\",\"rule_id\":22}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":2,\"top_order\":1,\"type\":22,\"id\":366}"
     * curl  http://127.0.0.1:8050/activity/edit  -H "Content-type: application/json" -X  POST -d "{\"actionConfig\":[{\"action_id\":21,\"params\":\"\"}],\"apply_times\":1,\"content\":\"法币单笔入金活动（新））\",\"created\":1611037608591,\"end_time\":1611901608591,\"execute_type\":1,\"is_need_audit\":1,\"is_push\":1,\"modified\":1611037608591,\"name\":\"法币单笔入金活动（新）\",\"publish_user_id\":\"1\",\"ruleConfig\":[{\"params\":\"{\\\"rewardType\\\":1,\\\"rewardTimes\\\":1,\\\"configList\\\":[{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":1000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":10},{\\\"exchangeCurrency\\\":\\\"ETH\\\",\\\"fiatAmount\\\":2000,\\\"fiatCurrency\\\":\\\"HKD\\\",\\\"firstDeposit\\\":false,\\\"rewardCurrency\\\":\\\"HKD\\\",\\\"rewardValue\\\":20}],\\\"frequency\\\":1}\",\"rule_id\":21}],\"saas_id\":\"kiki\",\"start_time\":1611037608591,\"status\":2,\"top_order\":1,\"type\":21,\"id\":365}"
     *
     * @param activity
     * @return
     */
    @RequestMapping("/edit")
    @ResponseBody
    public Boolean edit(@RequestBody Activity activity) {
        log.info("edit...{}", JSON.toJSONString(activity));
        try {
            activity.setSaas_id(activity.getSaas_id());
            activityService.update(activity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/update/status -X POST -d "id=365&status=1"
     *
     * @param id
     * @param status
     * @return
     */
    @RequestMapping("/update/status")
    @ResponseBody
    public Boolean update(@RequestParam Integer id,
                          @RequestParam Integer status) {
        log.info("updateStatus...id[{}] status[{}]", id, status);
        try {
            activityService.updateStatus(id, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/detail?id=365
     *
     * @param id
     * @return
     */
    @RequestMapping("/detail")
    @ResponseBody
    public JsonResult detail(@RequestParam Integer id) {
        log.info("updateStatus...id[{}]", id);
        try {
            JsonResult result = activityService.findDetailById(id);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/delete -X POST -d "id=365"
     *
     * @param id
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public JsonResult delete(@RequestParam Integer id) {
        log.info("updateStatus...id[{}] ", id);
        try {
            JsonResult result = activityService.delete(id, false);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/data/prepare -X POST -d "id=366&jobName=fiat_deposit_total_22"
     *
     * @param id
     * @return
     */
    @RequestMapping("/data/prepare")
    @ResponseBody
    public String dataPrepare(@RequestParam Integer id, String jobName) {
        log.info("dataPrepare...id[{}] ", id);
        try {

            JsonResult result = activityService.findDetailById(id);
            if (!result.getSuccess() || result.getObj() == null) {
                return "invalid activity!";
            }

            SchedLog schedLog = schedLogService.findByLatestBatch(jobName);
            if (schedLog == null) {
                return "max compute data process unready!";
            }

            Activity activity = (Activity) result.getObj();
            JSONObject object = JSONObject.parseObject(activity.getRuleConfig().get(0).getParams());
            List<FiatDepositActivityConfig> configList = object.getObject("configList", new TypeReference<List<FiatDepositActivityConfig>>() {
            });
            List<String> currencyList = configList.stream().map(f -> f.getFiatCurrency()).collect(Collectors.toList());

            boolean dealsuccess = currencyList.stream().map(currency -> {
                RangeResult<ActivityUserTotalData> records = activityUserTotalDataBuilder.list(schedLog.getBatch_pt(), id, currency, ActivityConstant.RecordStatus.RECORDED, null, 1);
                return CollectionUtils.isEmpty(records.list);
            }).anyMatch(f -> f == true);

            boolean rewardsuccess = currencyList.stream().map(currency -> {
                RangeResult<ActivityUserTotalData> records = activityUserTotalDataBuilder.list(schedLog.getBatch_pt(), id, currency, ActivityConstant.RecordStatus.COMPLETED, null, 1);
                return !CollectionUtils.isEmpty(records.list);
            }).anyMatch(f -> f == true);

            if (dealsuccess && rewardsuccess) {
                return "data prepare success!";
            }

            ActivityEventMassage massage = new ActivityEventMassage();
            massage.setId(id);
            massage.setAutoDataPrepare(false);
            massage.setType(ActivityConstant.RewardType.DATA_PROCESS.getCode());
            massage.setBatchNo(schedLog.getBatch_pt());
            massage.setJobName(jobName);
            ActivityResponse activityResponse = activityService.activityDataProcess(massage);
            if (activityResponse.isSuccess()) {
                return "data prepare processing!";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
        return null;
    }


    /**
     * curl  http://127.0.0.1:8050/activity/manual/reward -X POST -d "id=366"
     *
     * @param id
     * @return
     */
    @RequestMapping("/manual/reward")
    @ResponseBody
    public String manualReward(@RequestParam Integer id) {
        log.info("manualReward...id[{}] ", id);
        try {

            JsonResult result = activityService.findDetailById(id);
            if (!result.getSuccess() || result.getObj() == null) {
                return "invalid activity";
            }

            Activity activity = (Activity) result.getObj();
            long count = activityRecordsBuilder.countByActivityIdAndType(id, activity.getExecute_type(), new Date(), ActivityConstant.RecordStatus.RECORDED.getCode());
            if (count == 0) {
                return "manual reward success";
            }

            ActivityEventMassage massage = new ActivityEventMassage();
            massage.setId(id);
            massage.setType(ActivityConstant.RewardType.MANUAL_REWARD.getCode());
            ActivityResponse activityResponse = activityService.activityManualReward(massage);
            if (activityResponse.isSuccess()) {
                return "manual reward processing";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
        return null;
    }

    /**
     * curl  http://127.0.0.1:8080/activity/reward/retry -X POST -d "statusRequest=NOT_AWARD%2CAWARD_FAILED&size=3"
     *
     * @return
     */
    @RequestMapping("/reward/retry")
    @ResponseBody
    public String rewardRetry(@RequestParam String statusRequest, @RequestParam Integer size) {
        log.info("[reward retry] begin...");
        try {
            List<String> supportStatusList = Arrays.asList("AWARD_FAILED", "NOT_AWARD");
            List<String> statusList = Arrays.asList(statusRequest.trim().split(","));
            for( String status : statusList ) {
                if(!supportStatusList.contains(status)) {
                    return "[reward retry] status {} not support" + status;
                }
            }
            ActivityRewardPageParam activityRewardPageParam = new ActivityRewardPageParam();
            activityRewardPageParam.setStatusList(statusList);
            for( int offset = 0; ; offset += size){
                Page<ActivityCustomReward> result = activityCustomRewardStoreBuilder.findAllByStatus(activityRewardPageParam, offset, size);
                if (result == null || CollectionUtils.isEmpty(result.getRows())) {
                    break;
                }
                log.info("[reward retry] for {} fail items begin...", result.getRows().size());

                for (ActivityCustomReward reward : result.getRows()) {
                    log.info("[reward retry] for rewardId:{} begin", reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq());
                    LauncherParameter launcherParameter = new LauncherParameter();
                    launcherParameter.setActivityCustomReward(reward);
                    try {
                        activityRealTimeRewardTccService.reward(launcherParameter);
                        log.info("[reward retry] for rewardId:{} end", reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq());
                    } catch (Exception e) {
                        log.error("reward error rewardId:{}",  reward.getBatchId() + "-" + reward.getCustomerId() + "-" + reward.getSeq(), e);
                    }
                }
            }

            return "[reward retry] all end.";
        } catch (Exception exception) {
            log.info("[reward retry] error", exception);
            return exception.getMessage();
        }
    }

    /**
     * curl  http://127.0.0.1:8050/activity/activityTest -X POST -d "{ActivityMessage 类型的 json 格式的测试数据}"
     *
     * @param activityMessage
     * @return
     */
    @RequestMapping("/activityRewardTest")
    @ResponseBody
    public boolean activityRewardTest(@RequestBody ActivityMessage activityMessage) {
        log.info("activityRewardTest... activityMessage={}", JSONObject.toJSONString(activityMessage));
        try {
            // 校验必须的参数
            if (activityMessage == null || !StringUtils.isNoneBlank(activityMessage.getCustomerId(), activityMessage.getBusinessId(), activityMessage.getSaasId())
                    || activityMessage.getEvent() == null) {
                log.error("activityRewardTest, necessary parameter is missing, return false");
                return false;
            }
            // 发送充值完成的消息，触发奖励活动
            onsProducer.asyncSend(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("activityRewardTest, testMqMsg send success!");
                }

                @Override
                public void onException(OnExceptionContext onExceptionContext) {
                    onsProducer.send(topicConfig.getTopicActivity(), JSONObject.toJSONString(activityMessage));
                    log.info("activityRewardTest, testMqMsg send success!");
                }
            });
            return true;
        } catch (Exception e) {
            log.error("activityRewardTest, execute exception!", e);
            return false;
        }
    }

    /**
     * 刷新 KOL 邀请额外奖励的配置
     *
     * @return
     */
    @RequestMapping(value = "/inviteFirstDeposit/refresh", method = RequestMethod.POST)
    @ResponseBody
    public String inviteFirstDepositRefresh(HttpServletRequest request) {
        try {
            String result = null;
            if (kolExtraRewardService.reload()) {
                result = ActivityExceptionType.EXECUTION_SUCCEED.getCode();
            } else {
                result = ActivityExceptionType.KOL_EXTRA_REWARD_CONFIG_RELOAD_FAILED.getCode();
            }
            log.info("inviteFirstDepositRefresh, result:{}", result);
            return result;
        } catch (Exception e) {
            log.error("inviteFirstDepositRefresh process fail", e);
            return "error";
        }
    }

    @RequestMapping(value = "airdrop/refund", method = RequestMethod.GET)
    @ResponseBody
    public String refund(String id) {
        return releaseService.refundById(id) ? "success" : "fail";
    }

    @RequestMapping(value = "rt", method = RequestMethod.GET)
    @ResponseBody
    public Token refreshToken(@RequestParam("cid") String customerId, @RequestParam("platform") String platform){
        return authService.getAuthToken("monster", customerId, platform);
    }

    /**
     * 解绑 X 账号
     * curl  http://127.0.0.1:8080/activity/unbindx?saasId=xxx&socialName=xxx
     * @param saasId
     * @param socialName
     * @return
     */
    @RequestMapping(value = "unbindx", method = RequestMethod.GET)
    @ResponseBody
    public String unbindx(@RequestParam("saasId") String saasId,
                         @RequestParam("socialName") String socialName,
                         @RequestParam("socialPlatform") String socialPlatform){
        log.info("begin unbind {} with saasId {}, socialName {}", socialPlatform, saasId, socialName);
        if (StringUtils.isBlank(socialPlatform)) {
            socialPlatform = OpenSocialPlatformEnum.twitter.name();
        }
        List<TCustomerDTO> customerDTOS = remoteCustomerBindService.findTCustomerBySocialName(saasId, socialPlatform, socialName);
        if (Objects.nonNull(customerDTOS) && !customerDTOS.isEmpty()) {
            TCustomerDTO tCustomerDTO = customerDTOS.get(0);
            Boolean del1 = remoteCustomerBindService.unbindSocial(saasId, tCustomerDTO.getUid(), socialPlatform);
            log.info("del user bind twitter info, result = {}", del1);
            Boolean del2 = redisService.del(RedisKeyConst.ACTIVITY_AUTH_TOKEN.getKey(String.format("%s:%s:%s",
                    threePlatformProperties.getTwitter().getAuthVersion().get(saasId),
                    tCustomerDTO.getUid(),
                    threePlatformProperties.getTwitter().getClientId().get(saasId))));
            log.info("del user cache twitter auth info, result = {}", del2);
        } else {
            log.info("cannot find user {} {}", saasId, socialName);
        }
        log.info("unbind X end !");
        return "unbind x success";
    }

    @Resource
    @Lazy
    private MemberRankingJob memberRankingJob;
    @Resource
    @Lazy
    private BreadMemberRankingJob breadMemberRankingJob;
    @Resource
    @Lazy
    private ZeekMemberRankingJob zeekMemberRankingJob;


    /**
     * curl  http://127.0.0.1:8080/activity/rank "
     *
     * @return
     */
    @RequestMapping(value = "rank", method = RequestMethod.GET)
    @ResponseBody
    public String rank(){
        memberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "bread/rank", method = RequestMethod.GET)
    @ResponseBody
    public String breadRank(){
        breadMemberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "zeek/rank", method = RequestMethod.GET)
    @ResponseBody
    public String zeekRank(){
        zeekMemberRankingJob.ex();
        return "success";
    }

    @RequestMapping(value = "reset/auth", method = RequestMethod.GET)
    @ResponseBody
    public String resetAuth(){

        return "success";
    }
}


